#!/bin/bash

# EAS Build pre-install hook to create google-services.json from environment variable

echo "🔧 Setting up google-services.json from environment variable..."

if [ -n "$GOOGLE_SERVICES_JSON" ]; then
    echo "✅ GOOGLE_SERVICES_JSON environment variable found"
    echo "$GOOGLE_SERVICES_JSON" > google-services.json
    echo "✅ google-services.json created successfully"
    
    # Verify the file was created
    if [ -f "google-services.json" ]; then
        echo "✅ google-services.json file exists"
        echo "📄 File size: $(wc -c < google-services.json) bytes"
    else
        echo "❌ Failed to create google-services.json"
        exit 1
    fi
else
    echo "❌ GOOGLE_SERVICES_JSON environment variable not found"
    echo "Please make sure you've set the GOOGLE_SERVICES_JSON environment variable in your EAS project"
    exit 1
fi

echo "🎉 google-services.json setup completed"
