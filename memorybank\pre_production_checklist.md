# Pre-Production Checklist

This document outlines the necessary steps to take before creating a production build of the Giftmi application.

**1. Linting and Code Style**

A consistent and clean codebase is crucial for maintainability.

*   **Fix ESLint Configuration:**
    ESLint v9 introduced a new configuration file format. The existing `.eslintrc.js` needs to be migrated to `eslint.config.js`.

    **Action:**
    1.  Rename `.eslintrc.js` to `eslint.config.js`.
    2.  Update the content of `eslint.config.js` to the new format. A compatibility utility can be used, but a manual update is often cleaner. Here is a suggested new configuration:

    ```javascript
    // eslint.config.js
    import globals from "globals";
    import tseslint from "typescript-eslint";
    import expo from "eslint-config-expo";
    import prettier from "eslint-config-prettier";

    export default [
      { files: ["**/*.{js,mjs,cjs,ts,jsx,tsx}"] },
      { languageOptions: { globals: globals.browser } },
      ...tseslint.configs.recommended,
      expo,
      prettier,
      {
        rules: {
          'prettier/prettier': 'warn',
          'react/react-in-jsx-scope': 'off',
        }
      },
      {
        ignores: [
          "node_modules/",
          ".expo/",
          "babel.config.js",
          "metro.config.js",
        ]
      }
    ];
    ```
    *Note: This is a suggested configuration. You might need to install additional packages like `typescript-eslint` and `eslint-config-prettier` if they are not already dev dependencies.*

*   **Run Linter:**
    After fixing the configuration, run the linter to catch any potential issues.

    ```bash
    npm run lint
    ```

*   **Run Prettier:**
    Ensure consistent code formatting across the project.

    ```bash
    npx prettier --write .
    ```

**2. Testing**

A comprehensive test suite ensures application stability.

*   **Run All Tests:**
    Execute the test suite to ensure all existing functionality is working as expected.

    ```bash
    npm test
    ```

*   **Increase Test Coverage:**
    Consider writing additional tests for critical user flows and business logic, especially for new features.

**3. Dependency Management**

Keep your dependencies up-to-date and clean.

*   **Check for Unused Dependencies:**
    Remove any unused packages to reduce the final bundle size.

    ```bash
    npx depcheck
    ```

*   **Check for Outdated Dependencies:**
    Review and update outdated dependencies to get the latest bug fixes and security patches.

    ```bash
    npm outdated
    ```

**4. Configuration and Secrets Management**

Protecting sensitive information is a top priority.

*   **Secrets in Code:**
    The `firebaseConfig.ts` correctly uses environment variables (`process.env.EXPO_PUBLIC_FIREBASE_API_KEY`) for the API key. This is good practice.

*   **`google-services.json`:**
    **CRITICAL:** The `google-services.json` file contains an API key and other sensitive project information. This file should **NEVER** be committed to a public repository.

    **Action:**
    Add `google-services.json` to your `.gitignore` file immediately.

    ```
    # .gitignore
    google-services.json
    ```
    For CI/CD pipelines, this file should be provided securely (e.g., as a secret file or environment variable).

*   **EAS Configuration:**
    The `eas.json` file is configured to automatically increment the build number for production builds, which is a good practice.

**5. Asset Management**

Optimize assets to improve app performance.

*   **Image Optimization:**
    Ensure all images in the `assets/images` directory are compressed and appropriately sized for their use case to reduce app size and improve loading times.

*   **Unused Assets:**
    Review the assets directory and remove any unused images or fonts.

**6. Push Notifications**

*   **Production Configuration:**
    Double-check that your push notification setup (e.g., Firebase Cloud Messaging) is configured with production certificates and keys.

**7. Error Handling and Logging**

*   **Production Error Reporting:**
    Implement a robust error reporting service (e.g., Sentry, Bugsnag, or Firebase Crashlytics) to capture and analyze errors that occur in the production application.

**8. Build and Deployment**

*   **Create a Production Build:**
    Use the EAS CLI to create a production build for iOS and Android.

    ```bash
    # For Android
    eas build -p android --profile production

    # For iOS
    eas build -p ios --profile production
    ```

*   **Test on Real Devices:**
    Before submitting to the app stores, thoroughly test the production build on a variety of real Android and iOS devices to catch any device-specific issues.

This checklist should help ensure a smooth and successful production release.
